import React from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";

interface InvoiceEmailTrackingProps {
  invoiceId: Id<"invoices">;
  className?: string;
}

export function InvoiceEmailTracking({ invoiceId, className = "" }: InvoiceEmailTrackingProps) {
  const trackingData = useQuery(api.emailTracking.getInvoiceEmailTracking, { invoiceId });

  if (!trackingData || trackingData.length === 0) {
    return (
      <div className={`bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700 ${className}`}>
        <div className="flex items-center text-gray-500 dark:text-gray-400">
          <span className="text-lg mr-2">📭</span>
          <span className="text-sm">No email tracking data available</span>
        </div>
      </div>
    );
  }

  const totalEmails = trackingData.length;
  const openedEmails = trackingData.filter(t => t.openedAt).length;
  const totalOpens = trackingData.reduce((sum, t) => sum + t.openCount, 0);
  const openRate = totalEmails > 0 ? (openedEmails / totalEmails * 100) : 0;

  return (
    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">📧 Email Tracking</h3>
        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
          openRate > 0
            ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
            : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
        }`}>
          {openRate.toFixed(0)}% Open Rate
        </span>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{totalEmails}</div>
          <div className="text-xs text-gray-500 dark:text-gray-400">Sent</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">{openedEmails}</div>
          <div className="text-xs text-gray-500 dark:text-gray-400">Opened</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{totalOpens}</div>
          <div className="text-xs text-gray-500 dark:text-gray-400">Total Opens</div>
        </div>
      </div>

      {/* Email Details */}
      <div className="space-y-3">
        {trackingData.map((tracking) => (
          <div key={tracking._id} className="border-l-4 border-gray-200 dark:border-gray-600 pl-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className={`text-lg ${
                  tracking.openedAt
                    ? 'text-green-500 dark:text-green-400'
                    : 'text-gray-400 dark:text-gray-500'
                }`}>
                  {tracking.emailType === 'invoice_pdf' ? '📄' : '📧'}
                </span>
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {tracking.emailType === 'invoice_pdf' ? 'PDF Email' : 'Invoice Email'}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Sent {new Date(tracking.sentAt).toLocaleString()}
                  </div>
                </div>
              </div>

              <div className="text-right">
                {tracking.openedAt ? (
                  <div>
                    <div className="text-sm font-medium text-green-600 dark:text-green-400">
                      Opened {tracking.openCount} time{tracking.openCount !== 1 ? 's' : ''}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Last: {new Date(tracking.lastOpenedAt || tracking.openedAt).toLocaleString()}
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-gray-500 dark:text-gray-400">Not opened</div>
                )}
              </div>
            </div>

            {/* Email Type Badge */}
            <div className="mt-2">
              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                tracking.emailType === 'invoice_pdf'
                  ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300'
              }`}>
                {tracking.emailType.replace('_', ' ').toUpperCase()}
              </span>

              {tracking.openedAt && (
                <span className="ml-2 inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                  ✓ OPENED
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      {trackingData.some(t => t.openedAt) && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">📈 Recent Activity</h4>
          <div className="space-y-1">
            {trackingData
              .filter(t => t.openedAt)
              .sort((a, b) => (b.lastOpenedAt || b.openedAt || 0) - (a.lastOpenedAt || a.openedAt || 0))
              .slice(0, 3)
              .map((tracking) => (
                <div key={tracking._id} className="text-xs text-gray-600 dark:text-gray-300">
                  <span className="font-medium">
                    {tracking.emailType === 'invoice_pdf' ? 'PDF email' : 'Invoice email'}
                  </span>
                  {' '}opened{' '}
                  <span className="font-medium">
                    {new Date(tracking.lastOpenedAt || tracking.openedAt!).toLocaleString()}
                  </span>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Compact version for use in lists
export function InvoiceEmailTrackingBadge({ invoiceId, className = "" }: InvoiceEmailTrackingProps) {
  const trackingData = useQuery(api.emailTracking.getInvoiceEmailTracking, { invoiceId });

  if (!trackingData || trackingData.length === 0) {
    return null;
  }

  const totalEmails = trackingData.length;
  const openedEmails = trackingData.filter(t => t.openedAt).length;
  const totalOpens = trackingData.reduce((sum, t) => sum + t.openCount, 0);

  if (totalEmails === 0) return null;

  return (
    <div className={`inline-flex items-center space-x-1 ${className}`}>
      <span className={`text-sm ${
        openedEmails > 0
          ? 'text-green-600 dark:text-green-400'
          : 'text-gray-400 dark:text-gray-500'
      }`}>
        📧
      </span>
      <span className="text-xs text-gray-600 dark:text-gray-300">
        {openedEmails}/{totalEmails}
        {totalOpens > totalEmails && (
          <span className="text-gray-400 dark:text-gray-500"> ({totalOpens} opens)</span>
        )}
      </span>
    </div>
  );
}

// Hook for email tracking data
export function useInvoiceEmailTracking(invoiceId: Id<"invoices">) {
  const trackingData = useQuery(api.emailTracking.getInvoiceEmailTracking, { invoiceId });
  
  if (!trackingData) {
    return {
      isLoading: true,
      trackingData: [],
      stats: null,
    };
  }

  const totalEmails = trackingData.length;
  const openedEmails = trackingData.filter(t => t.openedAt).length;
  const totalOpens = trackingData.reduce((sum, t) => sum + t.openCount, 0);
  const openRate = totalEmails > 0 ? (openedEmails / totalEmails * 100) : 0;

  const stats = {
    totalEmails,
    openedEmails,
    totalOpens,
    openRate,
    hasTracking: totalEmails > 0,
    lastOpened: trackingData
      .filter(t => t.lastOpenedAt || t.openedAt)
      .sort((a, b) => (b.lastOpenedAt || b.openedAt || 0) - (a.lastOpenedAt || a.openedAt || 0))[0]
  };

  return {
    isLoading: false,
    trackingData,
    stats,
  };
}

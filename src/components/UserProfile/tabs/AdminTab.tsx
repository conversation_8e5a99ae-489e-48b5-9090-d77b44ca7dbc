import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { AdminCard } from "../AdminCard";
import { InviteUserModal } from "../modals/InviteUserModal";
import { InvitationsManagerModal } from "../modals/InvitationsManagerModal";

export function AdminTab() {
  const user = useQuery(api.auth.loggedInUser);
  const isMaster = user?.role === "master";
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showInvitationsModal, setShowInvitationsModal] = useState(false);

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Master-only User Management Section */}
      {isMaster && (
        <div>
          <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">Master Controls</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
            <AdminCard
              title="User Management"
              description="Manage users, roles, and permissions"
              icon="⭐"
              action="Manage"
            />
            <AdminCard
              title="Role Approvals"
              description="Review and approve admin role requests"
              icon="✅"
              action="Review"
            />
          </div>
        </div>
      )}

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">
          User Invitations
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
          <AdminCard
            title="Invite New User"
            description="Send invitation emails to new team members"
            icon="📧"
            action="Send Invite"
            onClick={() => setShowInviteModal(true)}
          />
          <AdminCard
            title="Manage Invitations"
            description="View, resend, or revoke pending invitations"
            icon="📋"
            action="Manage"
            onClick={() => setShowInvitationsModal(true)}
          />
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">
          {isMaster ? "Administrative Functions" : "User Management"}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
          <AdminCard
            title="Manage Users"
            description={isMaster ? "View and manage all system users" : "Add, edit, or remove user accounts"}
            icon="👥"
            action="Manage"
          />
          <AdminCard
            title="Role Permissions"
            description="Configure role-based access controls"
            icon="🔐"
            action="Configure"
          />
        </div>
      </div>

      <div>
        <h3 className="text-base md:text-lg font-semibold text-charcoal dark:text-gray-100 mb-3 md:mb-4">System Settings</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
          <AdminCard
            title="System Logs"
            description="View application logs and activity"
            icon="📋"
            action="View Logs"
          />
          <AdminCard
            title="Backup & Restore"
            description="Manage data backups and restoration"
            icon="💾"
            action="Manage"
          />
        </div>
      </div>

      {/* Modals */}
      {showInviteModal && (
        <InviteUserModal
          onClose={() => setShowInviteModal(false)}
          onInviteSent={() => {
            // Refresh invitations if the manager modal is open
            if (showInvitationsModal) {
              // The query will automatically refresh
            }
          }}
        />
      )}

      {showInvitationsModal && (
        <InvitationsManagerModal
          onClose={() => setShowInvitationsModal(false)}
        />
      )}
    </div>
  );
}
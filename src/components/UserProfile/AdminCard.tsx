import React from "react";

interface AdminCardProps {
  title: string;
  description: string;
  icon: string;
  action: string;
  onClick?: () => void;
}

export function AdminCard({ title, description, icon, action, onClick }: AdminCardProps) {
  const handleClick = () => {
    if (onClick) {
      onClick();
    } else {
      // Default navigation logic
      if (title.includes("User")) {
        // Navigate to user management
        window.dispatchEvent(new CustomEvent('navigate', { detail: { page: 'user-management' } }));
      } else if (title.includes("Role")) {
        // Navigate to admin approval
        window.dispatchEvent(new CustomEvent('navigate', { detail: { page: 'admin-approval' } }));
      } else if (title.includes("Settings") || title.includes("System")) {
        // Navigate to settings
        window.dispatchEvent(new CustomEvent('navigate', { detail: { page: 'settings' } }));
      }
    }
  };

  return (
    <button 
      onClick={handleClick}
      className="w-full p-4 md:p-6 bg-neutral dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600 hover:shadow-md dark:hover:shadow-lg transition-shadow touch-manipulation text-left"
    >
      <div className="flex items-start gap-3 md:gap-4">
        <div className="w-10 h-10 md:w-12 md:h-12 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center flex-shrink-0">
          <span className="text-lg md:text-2xl">{icon}</span>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-charcoal dark:text-gray-100 mb-1 text-sm md:text-base">{title}</h4>
          <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400 mb-2 md:mb-3">{description}</p>
          <span className="text-primary hover:text-primary/80 text-xs md:text-sm font-medium">
            {action} →
          </span>
        </div>
      </div>
    </button>
  );
}
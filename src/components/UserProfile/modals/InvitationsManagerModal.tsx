import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../../../convex/_generated/dataModel";

interface InvitationsManagerModalProps {
  onClose: () => void;
}

export function InvitationsManagerModal({ onClose }: InvitationsManagerModalProps) {
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isLoading, setIsLoading] = useState<string | null>(null);
  
  const invitations = useQuery(api.userInvitations.listInvitations, {
    status: statusFilter === "all" ? undefined : statusFilter,
    limit: 50,
  });
  
  const revokeInvitation = useMutation(api.userInvitations.revokeInvitation);
  const resendInvitation = useMutation(api.userInvitations.resendInvitation);

  const handleRevoke = async (invitationId: Id<"userInvitations">) => {
    setIsLoading(invitationId);
    try {
      await revokeInvitation({ invitationId });
      toast.success("Invitation revoked");
    } catch (error: any) {
      toast.error(error.message || "Failed to revoke invitation");
    } finally {
      setIsLoading(null);
    }
  };

  const handleResend = async (invitationId: Id<"userInvitations">) => {
    setIsLoading(invitationId);
    try {
      await resendInvitation({ invitationId });
      toast.success("Invitation resent");
    } catch (error: any) {
      toast.error(error.message || "Failed to resend invitation");
    } finally {
      setIsLoading(null);
    }
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusBadge = (status: string) => {
    const styles = {
      pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
      accepted: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
      expired: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
      revoked: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",
    };
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${styles[status as keyof typeof styles] || styles.pending}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-charcoal dark:text-gray-100">
            Manage Invitations
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation"
          >
            ✕
          </button>
        </div>

        <div className="mb-4">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-input w-auto"
          >
            <option value="all">All Invitations</option>
            <option value="pending">Pending</option>
            <option value="accepted">Accepted</option>
            <option value="expired">Expired</option>
            <option value="revoked">Revoked</option>
          </select>
        </div>

        <div className="overflow-y-auto max-h-96">
          {!invitations ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-gray-500 dark:text-gray-400 mt-2">Loading invitations...</p>
            </div>
          ) : invitations.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No invitations found</p>
            </div>
          ) : (
            <div className="space-y-3">
              {invitations.map((invitation) => (
                <div
                  key={invitation._id}
                  className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-neutral dark:bg-gray-700"
                >
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-medium text-charcoal dark:text-gray-100">
                          {invitation.email}
                        </h4>
                        {getStatusBadge(invitation.status)}
                      </div>
                      
                      <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        <div className="flex flex-wrap gap-4">
                          <span>Role: <strong>{invitation.role}</strong></span>
                          {invitation.department && (
                            <span>Department: <strong>{invitation.department}</strong></span>
                          )}
                        </div>
                        
                        <div className="flex flex-wrap gap-4">
                          <span>Invited by: <strong>{invitation.inviterName}</strong></span>
                          <span>Created: <strong>{formatDate(invitation.createdAt)}</strong></span>
                        </div>
                        
                        {invitation.status === "pending" && (
                          <div>Expires: <strong>{formatDate(invitation.expiresAt)}</strong></div>
                        )}
                        
                        {invitation.status === "accepted" && invitation.acceptedAt && (
                          <div className="flex flex-wrap gap-4">
                            <span>Accepted: <strong>{formatDate(invitation.acceptedAt)}</strong></span>
                            {invitation.acceptedByName && (
                              <span>By: <strong>{invitation.acceptedByName}</strong></span>
                            )}
                          </div>
                        )}
                        
                        {invitation.message && (
                          <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-600 rounded text-sm">
                            <strong>Message:</strong> {invitation.message}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {invitation.status === "pending" && (
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleResend(invitation._id)}
                          disabled={isLoading === invitation._id}
                          className="btn-secondary text-sm px-3 py-1 disabled:opacity-50"
                        >
                          {isLoading === invitation._id ? "..." : "Resend"}
                        </button>
                        <button
                          onClick={() => handleRevoke(invitation._id)}
                          disabled={isLoading === invitation._id}
                          className="bg-red-600 hover:bg-red-700 text-white text-sm px-3 py-1 rounded transition-colors disabled:opacity-50"
                        >
                          {isLoading === invitation._id ? "..." : "Revoke"}
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-600 mt-4">
          <button
            onClick={onClose}
            className="btn-secondary touch-manipulation"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}
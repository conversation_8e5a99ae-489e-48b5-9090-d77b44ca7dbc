import React, { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { toast } from "sonner";

interface InviteUserModalProps {
  onClose: () => void;
  onInviteSent?: () => void;
}

export function InviteUserModal({ onClose, onInviteSent }: InviteUserModalProps) {
  const [formData, setFormData] = useState({
    email: "",
    role: "staff",
    department: "",
    message: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const createInvitation = useMutation(api.userInvitations.createInvitation);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email) {
      toast.error("Email is required");
      return;
    }

    setIsLoading(true);
    try {
      await createInvitation({
        email: formData.email,
        role: formData.role,
        department: formData.department || undefined,
        message: formData.message || undefined,
      });
      
      toast.success("Invitation sent successfully");
      onInviteSent?.();
      onClose();
    } catch (error: any) {
      toast.error(error.message || "Failed to send invitation");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-md">
        <div className="flex justify-between items-center mb-4 md:mb-6">
          <h2 className="text-lg md:text-xl font-semibold text-charcoal dark:text-gray-100">
            Invite New User
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 text-xl p-2 touch-manipulation"
          >
            ✕
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="form-label">Email Address</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="form-input"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <label className="form-label">Role</label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              className="form-input"
              required
            >
              <option value="staff">Staff</option>
              <option value="admin">Admin</option>
            </select>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Staff can create and manage customers/jobs. Admins can also invite users.
            </p>
          </div>

          <div>
            <label className="form-label">Department (Optional)</label>
            <input
              type="text"
              value={formData.department}
              onChange={(e) => setFormData({ ...formData, department: e.target.value })}
              className="form-input"
              placeholder="e.g., Service, Sales, Admin"
            />
          </div>

          <div>
            <label className="form-label">Personal Message (Optional)</label>
            <textarea
              value={formData.message}
              onChange={(e) => setFormData({ ...formData, message: e.target.value })}
              className="form-input"
              rows={3}
              maxLength={500}
              placeholder="Add a personal message to the invitation email..."
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {formData.message.length}/500 characters
            </p>
          </div>

          <div className="flex flex-col md:flex-row gap-3 pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary flex-1 touch-manipulation disabled:opacity-50"
            >
              {isLoading ? "Sending..." : "Send Invitation"}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary flex-1 touch-manipulation"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
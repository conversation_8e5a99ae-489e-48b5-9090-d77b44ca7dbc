import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useConvexAuth } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useAuthActions } from "@convex-dev/auth/react";
import { toast } from "sonner";

export function InvitePage() {
  // Extract token from URL path
  const token = window.location.pathname.split('/invite/')[1];

  // Validate token format (basic check)
  const isValidTokenFormat = token && token.length >= 16 && /^[A-Za-z0-9]+$/.test(token);

  // State management
  const [formData, setFormData] = useState({
    name: "",
    password: "",
    confirmPassword: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'loading' | 'form' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>("");

  // Convex hooks
  const invitation = useQuery(api.userInvitations.getInvitationByToken,
    token ? { token } : "skip"
  );
  const acceptInvitationForCurrentUser = useMutation(api.userInvitations.acceptInvitationForCurrentUser);
  const { signIn } = useAuthActions();
  const { isAuthenticated } = useConvexAuth();

  // Check invitation validity on mount
  useEffect(() => {
    if (!token) {
      setErrorMessage("No invitation token found in URL");
      setStep('error');
      return;
    }

    if (!isValidTokenFormat) {
      setErrorMessage("Invalid invitation token format");
      setStep('error');
      return;
    }

    if (invitation === undefined) {
      // Still loading
      return;
    }

    if (invitation === null) {
      // Invalid or expired invitation
      setErrorMessage("This invitation link is invalid or has expired. Please contact an administrator for a new invitation.");
      setStep('error');
      return;
    }

    // Valid invitation found
    setStep('form');
    setErrorMessage("");
  }, [token, invitation, isValidTokenFormat]);

  // Handle post-signup invitation acceptance
  useEffect(() => {
    if (isAuthenticated && step === 'success' && token && isLoading) {
      // User just signed up successfully, now accept the invitation
      acceptInvitationForCurrentUser({ token })
        .then(() => {
          console.log('Invitation accepted successfully');
          setIsLoading(false);
          toast.success('Welcome to Bernie\'s Heating! Redirecting...');
          // Redirect after successful invitation acceptance
          setTimeout(() => {
            window.location.href = '/';
          }, 1500);
        })
        .catch((error) => {
          console.error('Failed to accept invitation:', error);
          setIsLoading(false);
          toast.error('Account created but failed to set role. Please contact an administrator.');
          // Still redirect to main app
          setTimeout(() => {
            window.location.href = '/';
          }, 3000);
        });
    }
  }, [isAuthenticated, step, token, isLoading, acceptInvitationForCurrentUser]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.name.trim()) {
      toast.error("Name is required");
      return;
    }

    if (formData.name.trim().length < 2) {
      toast.error("Name must be at least 2 characters long");
      return;
    }

    if (!formData.password) {
      toast.error("Password is required");
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    if (formData.password.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }

    // Check for common password patterns
    if (formData.password.toLowerCase().includes('password') ||
        formData.password === '12345678' ||
        formData.password === 'qwertyui') {
      toast.error("Please choose a more secure password");
      return;
    }

    if (!invitation) {
      toast.error("Invalid invitation. Please try again.");
      return;
    }

    setIsLoading(true);
    try {
      // Sign up the user with Convex Auth
      await signIn("password", {
        email: invitation.email,
        password: formData.password,
        name: formData.name.trim(),
        flow: "signUp",
      });

      // Set success state - the useEffect will handle invitation acceptance
      setStep('success');
      toast.success("Account created successfully! Setting up your role...");

    } catch (error: any) {
      console.error('Signup error:', error);
      let errorMessage = "Failed to create account";

      if (error.message.includes("already exists") || error.message.includes("duplicate")) {
        errorMessage = "An account with this email already exists. Please sign in instead.";
      } else if (error.message.includes("password")) {
        errorMessage = "Password does not meet requirements. Please try a different password.";
      } else if (error.message.includes("email")) {
        errorMessage = "Invalid email address. Please check and try again.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
      setIsLoading(false);
    }
    // Note: Don't set isLoading to false here - let the useEffect handle it after invitation acceptance
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  // Loading state
  if (step === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="spinner h-8 w-8 mx-auto mb-4"></div>
          <p className="text-gray-600">Validating invitation...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (step === 'error') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <div className="max-w-md w-full text-center">
          <div className="bg-white rounded-lg shadow-md p-8">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Invalid Invitation</h1>
            <p className="text-gray-600 mb-6">
              {errorMessage || "This invitation link is invalid or has expired. Please contact an administrator for a new invitation."}
            </p>
            <div className="space-y-3">
              <button
                onClick={() => window.location.href = '/'}
                className="w-full bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                Go to Sign In
              </button>
              <button
                onClick={() => window.location.reload()}
                className="w-full bg-gray-200 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-300 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Success state
  if (step === 'success') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <div className="max-w-md w-full text-center">
          <div className="bg-white rounded-lg shadow-md p-8">
            <div className="text-green-500 text-6xl mb-4">✅</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Welcome to Bernie's Heating!</h1>
            <p className="text-gray-600 mb-6">
              {isLoading
                ? "Setting up your account and role..."
                : "Your account has been created successfully. You will be redirected to the application shortly."
              }
            </p>
            <div className="spinner h-6 w-6 mx-auto"></div>
          </div>
        </div>
      </div>
    );
  }

  // Form state - Complete account setup
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-lg shadow-md p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <img src="/images/logo.png" alt="Bernie's Heating" className="h-16 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Complete Your Account</h1>
            <p className="text-gray-600">
              You've been invited to join Bernie's Heating CRM
            </p>
          </div>

          {/* Invitation Details */}
          {invitation && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="font-medium text-blue-900 mb-2">Invitation Details</h3>
              <div className="text-sm text-blue-800 space-y-1">
                <p><span className="font-medium">Email:</span> {invitation.email}</p>
                <p><span className="font-medium">Role:</span> {invitation.role}</p>
                {invitation.department && (
                  <p><span className="font-medium">Department:</span> {invitation.department}</p>
                )}
                {invitation.message && (
                  <p><span className="font-medium">Message:</span> {invitation.message}</p>
                )}
                <p><span className="font-medium">Invited by:</span> {invitation.inviterName}</p>
              </div>
            </div>
          )}

          {/* Account Setup Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Full Name
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your full name"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                required
                minLength={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Create a password (min. 8 characters)"
              />
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                Confirm Password
              </label>
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Confirm your password"
              />
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="spinner h-4 w-4 mr-2"></div>
                  Creating Account...
                </div>
              ) : (
                'Complete Account Setup'
              )}
            </button>
          </form>

          {/* Footer */}
          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500">
              Already have an account?{' '}
              <button
                onClick={() => window.location.href = '/'}
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                Sign In
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
import React from "react";

export function InvitePage() {
  // Debug logging
  console.log('🎉 InvitePage component mounted successfully!');
  console.log('Full URL:', window.location.href);
  console.log('Pathname:', window.location.pathname);
  
  // Extract token from URL path
  const token = window.location.pathname.split('/invite/')[1];
  console.log('Extracted token:', token);
  
  // Simple test render
  if (!token) {
    return (
      <div style={{ padding: '20px', textAlign: 'center', minHeight: '100vh', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
        <h1 style={{ color: 'red' }}>🔍 DEBUG: InvitePage Loaded</h1>
        <p>URL: {window.location.href}</p>
        <p>No token found in URL</p>
      </div>
    );
  }
  
  return (
    <div style={{ padding: '20px', textAlign: 'center', minHeight: '100vh', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
      <h1 style={{ color: 'green' }}>🎉 SUCCESS: InvitePage Working!</h1>
      <p>URL: {window.location.href}</p>
      <p>Token: {token}</p>
      <p>The invitation page is loading correctly!</p>
      <button onClick={() => window.location.href = '/'} style={{ padding: '10px 20px', marginTop: '20px' }}>
        Go Back to Main App
      </button>
    </div>
  );
}
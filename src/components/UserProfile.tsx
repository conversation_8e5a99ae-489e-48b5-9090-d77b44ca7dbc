import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";
import { ProfileCard } from "./UserProfile/ProfileCard";
import { TabContent } from "./UserProfile/TabContent";
import { EditProfileModal } from "./UserProfile/EditProfileModal";

// User profile interface with role-based features
export function UserProfile() {
  const user = useQuery(api.auth.loggedInUser);
  const userProfile = useQuery(api.users.getProfile);
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");

  if (!user || !userProfile) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 md:space-y-6 pb-20 md:pb-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-charcoal dark:text-gray-100">User Profile</h1>
          <p className="text-gray-600 dark:text-gray-400 text-sm md:text-base">Manage your account settings and preferences</p>
        </div>
      </div>

      {/* Profile Card */}
      <ProfileCard 
        user={user} 
        userProfile={userProfile} 
        onEdit={() => setIsEditing(true)} 
      />

      {/* Tabs */}
      <div className="card">
        <div className="border-b border-gray-100 dark:border-gray-700">
          <nav className="flex overflow-x-auto px-4 md:px-6">
            {[
              { id: "overview", label: "Overview", icon: "📊" },
              { id: "settings", label: "Settings", icon: "⚙️" },
              { id: "security", label: "Security", icon: "🔒" },
              ...(userProfile.role === "admin" || userProfile.role === "master"
                ? [{ id: "admin", label: "Admin", icon: "👑" }]
                : [])
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-3 md:py-4 px-3 md:px-4 border-b-2 font-medium text-sm whitespace-nowrap transition-colors touch-manipulation ${
                  activeTab === tab.id
                    ? "border-primary text-primary"
                    : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                }`}
              >
                <span>{tab.icon}</span>
                <span className="hidden sm:inline">{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-4 md:p-6">
          <TabContent activeTab={activeTab} userProfile={userProfile} />
        </div>
      </div>

      {/* Edit Profile Modal */}
      {isEditing && (
        <EditProfileModal
          user={user}
          userProfile={userProfile}
          onClose={() => setIsEditing(false)}
          onSuccess={() => {
            setIsEditing(false);
            toast.success("Profile updated successfully");
          }}
        />
      )}
    </div>
  );
}

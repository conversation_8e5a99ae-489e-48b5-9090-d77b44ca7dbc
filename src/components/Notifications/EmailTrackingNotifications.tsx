import React, { useEffect, useRef } from "react";
import { useQuery } from "convex/react";
import { api } from "../../../convex/_generated/api";
import { toast } from "../../utils/toast";

interface EmailTrackingNotificationsProps {
  enabled?: boolean;
}

export function EmailTrackingNotifications({ enabled = true }: EmailTrackingNotificationsProps) {
  const notifications = useQuery(api.notifications.subscribeToNotifications, enabled ? {} : "skip");
  const previousNotificationsRef = useRef<any[]>([]);

  useEffect(() => {
    if (!enabled || !notifications?.notifications) return;

    const currentNotifications = notifications.notifications;
    const previousNotifications = previousNotificationsRef.current;

    // Find new email tracking notifications
    const newEmailNotifications = currentNotifications.filter(notification => {
      if (notification.type !== 'email_opened') return false;
      
      // Check if this notification is new (not in previous list)
      const isNew = !previousNotifications.some(prev => prev._id === notification._id);
      return isNew;
    });

    // Show toast notifications for new email opens
    newEmailNotifications.forEach(notification => {
      if (notification.data?.invoiceDetails) {
        const { invoiceDetails } = notification.data;
        toast.success(
          `📧 Invoice ${invoiceDetails.invoiceNumber} opened by ${invoiceDetails.customerName}`,
          {
            duration: 5000,
            description: `Total: $${invoiceDetails.total.toFixed(2)} • Just now`,
          }
        );
      } else {
        toast.success(
          `📧 ${notification.title}`,
          {
            duration: 4000,
            description: notification.message,
          }
        );
      }
    });

    // Update the reference for next comparison
    previousNotificationsRef.current = currentNotifications;
  }, [notifications, enabled]);

  return null; // This component only handles notifications, no UI
}

// Hook for email tracking notifications
export function useEmailTrackingNotifications(enabled: boolean = true) {
  const notifications = useQuery(api.notifications.getEmailTrackingNotifications, { limit: 20 });
  const unreadCount = useQuery(api.notifications.getUnreadCount);
  
  return {
    notifications: notifications || [],
    unreadCount: unreadCount || 0,
    enabled,
  };
}

// Component to display email tracking stats
export function EmailTrackingStats() {
  const stats = useQuery(api.notifications.getNotificationStats);
  const analytics = useQuery(api.emailTracking.getEmailTrackingAnalytics, { timeRange: '7d' });

  if (!stats || !analytics) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 p-6 border border-gray-200 dark:border-gray-700">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 p-6 border border-gray-200 dark:border-gray-700">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">📧 Email Tracking</h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{analytics.totalSent}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Emails Sent</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">{analytics.totalOpened}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Emails Opened</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{analytics.openRate.toFixed(1)}%</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Open Rate</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{analytics.recentOpens}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">Today</div>
        </div>
      </div>

      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Recent Activity</h4>
        <div className="text-sm text-gray-600 dark:text-gray-300">
          <div className="flex justify-between">
            <span>Email notifications:</span>
            <span className="font-medium">{stats.byType.email_opened || 0}</span>
          </div>
          <div className="flex justify-between">
            <span>Unread notifications:</span>
            <span className="font-medium">{stats.unread}</span>
          </div>
          <div className="flex justify-between">
            <span>This week:</span>
            <span className="font-medium">{stats.thisWeek}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Component to display recent email opens
export function RecentEmailOpens() {
  const recentOpens = useQuery(api.emailTracking.getRecentEmailOpens, { limit: 10 });

  if (!recentOpens) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 p-6 border border-gray-200 dark:border-gray-700">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex space-x-3">
                <div className="h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 p-6 border border-gray-200 dark:border-gray-700">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">📬 Recent Email Opens</h3>
      
      {recentOpens.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <div className="text-4xl mb-2">📭</div>
          <p>No email opens yet</p>
          <p className="text-sm">Email tracking data will appear here when customers open your invoices</p>
        </div>
      ) : (
        <div className="space-y-4">
          {recentOpens.map((tracking) => (
            <div key={tracking._id} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                  <span className="text-green-600 dark:text-green-400 text-sm">📧</span>
                </div>
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {tracking.invoiceData ? (
                      <>Invoice {tracking.invoiceData.invoiceNumber} opened</>
                    ) : (
                      <>Email opened</>
                    )}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {tracking.openCount > 1 ? `${tracking.openCount} times` : 'First time'}
                  </p>
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {tracking.invoiceData ? (
                    <>
                      {tracking.invoiceData.customerName} • ${tracking.invoiceData.total.toFixed(2)}
                    </>
                  ) : (
                    <>
                      {tracking.recipientEmail}
                    </>
                  )}
                </p>

                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {tracking.lastOpenedAt ? (
                    <>Last opened {new Date(tracking.lastOpenedAt).toLocaleString()}</>
                  ) : tracking.openedAt ? (
                    <>Opened {new Date(tracking.openedAt).toLocaleString()}</>
                  ) : (
                    <>Sent {new Date(tracking.sentAt).toLocaleString()}</>
                  )}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

import { v } from "convex/values";
import { query, mutation, action } from "./_generated/server";
import { api } from "./_generated/api";
import { getCurrentUser, getCurrentUserWithRole, checkAdminPermission } from "./auth";
import { validateEmail, sanitizeString } from "./security/inputValidation";

// Generate a secure random token for invitations
function generateInviteToken(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let token = '';
  for (let i = 0; i < 32; i++) {
    token += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return token;
}

// Send invitation email
export const sendInvitationEmail = action({
  args: {
    email: v.string(),
    inviterName: v.string(),
    companyName: v.string(),
    token: v.string(),
    role: v.string(),
    message: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const resendApiKey = process.env.CONVEX_RESEND_API_KEY;
    if (!resendApiKey) {
      throw new Error("CONVEX_RESEND_API_KEY not configured");
    }

    const fromEmail = process.env.USERS_FROM_EMAIL || 'Bernie\'s Heating <<EMAIL>>';
    const inviteUrl = `${process.env.CONVEX_SITE_URL || 'http://localhost:5173'}/invite/${args.token}`;
    
    const emailContent = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #2563eb; margin: 0;">You're Invited to Join ${args.companyName}</h1>
        </div>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <p style="margin: 0 0 15px 0; font-size: 16px;">Hi there!</p>
          <p style="margin: 0 0 15px 0; font-size: 16px;">
            <strong>${args.inviterName}</strong> has invited you to join <strong>${args.companyName}</strong> 
            as a <strong>${args.role}</strong> member.
          </p>
          ${args.message ? `
            <div style="background: white; padding: 15px; border-left: 4px solid #2563eb; margin: 15px 0;">
              <p style="margin: 0; font-style: italic;">"${args.message}"</p>
            </div>
          ` : ''}
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${inviteUrl}" 
             style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
            Accept Invitation
          </a>
        </div>

        <div style="font-size: 14px; color: #6b7280; margin-top: 30px;">
          <p>This invitation will expire in 7 days.</p>
          <p>If you can't click the button above, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #2563eb;">${inviteUrl}</p>
        </div>

        <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; font-size: 12px; color: #6b7280; text-align: center;">
          <p>This email was sent by ${args.companyName}. If you didn't expect this invitation, you can safely ignore this email.</p>
        </div>
      </div>
    `;

    try {
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${resendApiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: fromEmail,
          to: [args.email],
          subject: `You're invited to join ${args.companyName}`,
          html: emailContent,
        }),
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Resend API error:', response.status, errorData);
        throw new Error(`Failed to send email: ${response.status}`);
      }

      const result = await response.json();
      console.log('Invitation email sent successfully:', result);
      return result;
    } catch (error) {
      console.error('Error sending invitation email:', error);
      throw new Error('Failed to send invitation email');
    }
  },
});

// Create and send user invitation
export const createInvitation = mutation({
  args: {
    email: v.string(),
    role: v.string(),
    department: v.optional(v.string()),
    message: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user has admin permissions
    const user = await checkAdminPermission(ctx);
    const userId = await getCurrentUser(ctx);

    // Validate email
    if (!validateEmail(args.email)) {
      throw new Error("Invalid email address");
    }

    // Sanitize inputs
    const email = sanitizeString(args.email, { required: true, maxLength: 255 }).sanitized?.toLowerCase();
    const role = sanitizeString(args.role, { required: true, maxLength: 50 }).sanitized;
    const department = args.department ? sanitizeString(args.department, { maxLength: 100 }).sanitized : undefined;
    const message = args.message ? sanitizeString(args.message, { maxLength: 500 }).sanitized : undefined;

    if (!email || !role) {
      throw new Error("Email and role are required");
    }

    // Validate role
    if (!['staff', 'admin'].includes(role)) {
      throw new Error("Invalid role. Must be 'staff' or 'admin'");
    }

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("email", (q) => q.eq("email", email))
      .first();

    if (existingUser) {
      throw new Error("A user with this email already exists");
    }

    // Check if there's already a pending invitation
    const existingInvitation = await ctx.db
      .query("userInvitations")
      .withIndex("by_email", (q) => q.eq("email", email))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .first();

    if (existingInvitation) {
      // Instead of throwing an error, we could either:
      // 1. Revoke the old invitation and create a new one
      // 2. Or just resend the existing invitation
      
      // Let's revoke the old one and create a new one
      await ctx.db.patch(existingInvitation._id, {
        status: "revoked",
        updatedAt: Date.now(),
      });
    }

    // Generate invitation token
    const token = generateInviteToken();
    const now = Date.now();
    const expiresAt = now + (7 * 24 * 60 * 60 * 1000); // 7 days

    // Create invitation record
    const invitationId = await ctx.db.insert("userInvitations", {
      email,
      role,
      department,
      invitedBy: userId,
      status: "pending",
      token,
      expiresAt,
      message,
      createdAt: now,
      updatedAt: now,
    });

    // Schedule email sending
    await ctx.scheduler.runAfter(0, api.userInvitations.sendInvitationEmail, {
      email,
      inviterName: user.name || "Admin",
      companyName: "Bernie's Heating",
      token,
      role,
      message,
    });

    return {
      invitationId,
      token,
      expiresAt,
    };
  },
});

// Get invitation by token (for signup flow)
export const getInvitationByToken = query({
  args: { token: v.string() },
  handler: async (ctx, args) => {
    // Validate token format
    if (!args.token || args.token.length < 16 || !/^[A-Za-z0-9]+$/.test(args.token)) {
      return null;
    }

    const invitation = await ctx.db
      .query("userInvitations")
      .withIndex("by_token", (q) => q.eq("token", args.token))
      .first();

    if (!invitation) {
      return null;
    }

    // Check if invitation is expired
    if (invitation.expiresAt < Date.now()) {
      // Mark as expired if not already
      if (invitation.status === "pending") {
        await ctx.db.patch(invitation._id, {
          status: "expired",
          updatedAt: Date.now(),
        });
      }
      return null;
    }

    // Only return pending invitations
    if (invitation.status !== "pending") {
      return null;
    }

    // Get inviter information
    const inviter = await ctx.db.get(invitation.invitedBy);

    return {
      email: invitation.email,
      role: invitation.role,
      department: invitation.department,
      message: invitation.message,
      inviterName: inviter?.name || "Admin",
      expiresAt: invitation.expiresAt,
    };
  },
});

// Accept invitation (called during signup)
export const acceptInvitation = mutation({
  args: {
    token: v.string(),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const invitation = await ctx.db
      .query("userInvitations")
      .withIndex("by_token", (q) => q.eq("token", args.token))
      .first();

    if (!invitation || invitation.status !== "pending") {
      throw new Error("Invalid or expired invitation");
    }

    // Check if invitation is expired
    if (invitation.expiresAt < Date.now()) {
      await ctx.db.patch(invitation._id, {
        status: "expired",
        updatedAt: Date.now(),
      });
      throw new Error("Invitation has expired");
    }

    // Mark invitation as accepted
    await ctx.db.patch(invitation._id, {
      status: "accepted",
      acceptedAt: Date.now(),
      acceptedBy: args.userId,
      updatedAt: Date.now(),
    });

    // Set user role and department
    await ctx.db.patch(args.userId, {
      role: invitation.role,
      department: invitation.department,
    });

    return {
      success: true,
      role: invitation.role,
      department: invitation.department,
    };
  },
});

// Accept invitation for current user (called after successful signup)
export const acceptInvitationForCurrentUser = mutation({
  args: {
    token: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate token format
    if (!args.token || args.token.length < 16 || !/^[A-Za-z0-9]+$/.test(args.token)) {
      throw new Error("Invalid invitation token format");
    }

    // Get the current authenticated user
    const userId = await getCurrentUser(ctx);

    const invitation = await ctx.db
      .query("userInvitations")
      .withIndex("by_token", (q) => q.eq("token", args.token))
      .first();

    if (!invitation) {
      throw new Error("Invitation not found");
    }

    if (invitation.status !== "pending") {
      throw new Error(`Invitation is ${invitation.status} and cannot be accepted`);
    }

    // Check if invitation is expired
    if (invitation.expiresAt < Date.now()) {
      await ctx.db.patch(invitation._id, {
        status: "expired",
        updatedAt: Date.now(),
      });
      throw new Error("Invitation has expired");
    }

    // Verify the email matches the current user
    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User account not found");
    }

    if (!user.email || user.email !== invitation.email) {
      throw new Error("Invitation email does not match your account email");
    }

    // Check if user already has a role assigned (prevent duplicate acceptance)
    if (user.role && user.role !== "staff") {
      console.log(`User ${userId} already has role ${user.role}, updating to invitation role ${invitation.role}`);
    }

    // Mark invitation as accepted
    await ctx.db.patch(invitation._id, {
      status: "accepted",
      acceptedAt: Date.now(),
      acceptedBy: userId,
      updatedAt: Date.now(),
    });

    // Set user role and department
    await ctx.db.patch(userId, {
      role: invitation.role,
      department: invitation.department,
    });

    console.log(`Invitation accepted successfully for user ${userId}, role: ${invitation.role}`);

    return {
      success: true,
      role: invitation.role,
      department: invitation.department,
    };
  },
});

// List invitations (admin only)
export const listInvitations = query({
  args: {
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    // Check if user has admin permissions
    await checkAdminPermission(ctx);

    const limit = args.limit || 50;
    
    let invitations;
    if (args.status) {
      invitations = await ctx.db
        .query("userInvitations")
        .withIndex("by_status", (q) => q.eq("status", args.status!))
        .order("desc")
        .take(limit);
    } else {
      invitations = await ctx.db
        .query("userInvitations")
        .order("desc")
        .take(limit);
    }

    // Get inviter information for each invitation
    const invitationsWithInviters = await Promise.all(
      invitations.map(async (invitation) => {
        const inviter = await ctx.db.get(invitation.invitedBy);
        const acceptedBy = invitation.acceptedBy ? await ctx.db.get(invitation.acceptedBy) : null;
        
        return {
          ...invitation,
          inviterName: inviter?.name || "Unknown",
          acceptedByName: acceptedBy?.name || null,
        };
      })
    );

    return invitationsWithInviters;
  },
});

// Revoke invitation
export const revokeInvitation = mutation({
  args: { invitationId: v.id("userInvitations") },
  handler: async (ctx, args) => {
    // Check if user has admin permissions
    await checkAdminPermission(ctx);

    const invitation = await ctx.db.get(args.invitationId);
    if (!invitation) {
      throw new Error("Invitation not found");
    }

    if (invitation.status !== "pending") {
      throw new Error("Can only revoke pending invitations");
    }

    await ctx.db.patch(args.invitationId, {
      status: "revoked",
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Resend invitation
export const resendInvitation = mutation({
  args: { invitationId: v.id("userInvitations") },
  handler: async (ctx, args) => {
    // Check if user has admin permissions
    const user = await checkAdminPermission(ctx);

    const invitation = await ctx.db.get(args.invitationId);
    if (!invitation) {
      throw new Error("Invitation not found");
    }

    if (invitation.status !== "pending") {
      throw new Error("Can only resend pending invitations");
    }

    // Generate new token and extend expiry
    const newToken = generateInviteToken();
    const now = Date.now();
    const newExpiresAt = now + (7 * 24 * 60 * 60 * 1000); // 7 days

    await ctx.db.patch(args.invitationId, {
      token: newToken,
      expiresAt: newExpiresAt,
      updatedAt: now,
    });

    // Schedule email sending
    await ctx.scheduler.runAfter(0, api.userInvitations.sendInvitationEmail, {
      email: invitation.email,
      inviterName: user.name || "Admin",
      companyName: "Bernie's Heating",
      token: newToken,
      role: invitation.role,
      message: invitation.message,
    });

    return { success: true };
  },
});

// Clean up expired invitations
export const cleanupExpiredInvitations = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    const expiredInvitations = await ctx.db
      .query("userInvitations")
      .withIndex("by_status", (q) => q.eq("status", "pending"))
      .filter((q) => q.lt(q.field("expiresAt"), now))
      .collect();

    let cleanedCount = 0;
    for (const invitation of expiredInvitations) {
      await ctx.db.patch(invitation._id, {
        status: "expired",
        updatedAt: now,
      });
      cleanedCount++;
    }

    console.log(`Cleaned up ${cleanedCount} expired invitations`);
    return { cleanedCount };
  },
});
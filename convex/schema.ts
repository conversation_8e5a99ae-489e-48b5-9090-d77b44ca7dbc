import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

const applicationTables = {
  customers: defineTable({
    name: v.string(),
    email: v.string(),
    phone: v.string(),
    company: v.optional(v.string()),
    industry: v.optional(v.string()),
    address: v.string(),
    city: v.string(),
    state: v.string(),
    zipCode: v.string(),
    status: v.optional(v.string()), // Make optional to support existing data
    notes: v.optional(v.string()),
    county: v.optional(v.string()),
    createdAt: v.optional(v.number()), // Make optional to support existing data
    updatedAt: v.optional(v.number()), // Make optional to support existing data
    createdBy: v.id("users"),
  })
    .index("by_created_by", ["createdBy"])
    .index("by_email", ["email"])
    .index("by_status", ["status"])
    .index("by_company", ["company"]),

  jobs: defineTable({
    customerId: v.id("customers"),
    title: v.string(),
    description: v.string(),
    status: v.string(), // scheduled, in-progress, completed, cancelled
    priority: v.string(), // low, medium, high, emergency
    scheduledDate: v.optional(v.number()),
    completedDate: v.optional(v.number()),
    estimatedHours: v.optional(v.number()),
    actualHours: v.optional(v.number()),
    notes: v.optional(v.string()),
    assignedTo: v.id("users"),
    createdBy: v.id("users"),
  })
    .index("by_created_by", ["createdBy"])
    .index("by_customer", ["customerId"])
    .index("by_status", ["status"])
    .index("by_assigned_to", ["assignedTo"]),

  products: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    category: v.string(),
    price: v.number(),
    cost: v.optional(v.number()),
    sku: v.optional(v.string()),
    stockQuantity: v.optional(v.number()),
    isActive: v.boolean(),
    createdBy: v.id("users"),
  })
    .index("by_created_by", ["createdBy"])
    .index("by_category", ["category"])
    .index("by_sku", ["sku"]),

  invoices: defineTable({
    invoiceNumber: v.string(),
    customerId: v.id("customers"),
    jobId: v.optional(v.id("jobs")),
    issueDate: v.number(),
    dueDate: v.number(),
    status: v.string(), // draft, sent, paid, overdue, cancelled
    subtotal: v.number(),
    taxRate: v.number(),
    taxAmount: v.number(),
    discountAmount: v.number(),
    total: v.number(),
    notes: v.optional(v.string()),
    terms: v.optional(v.string()), // Add terms field
    paidDate: v.optional(v.number()),
    pdfStorageId: v.optional(v.string()),
    htmlStorageId: v.optional(v.string()),
    createdAt: v.number(), // Add required timestamps
    updatedAt: v.number(),
    createdBy: v.id("users"),
    items: v.array(  // Add items array directly to invoice
      v.object({
        description: v.string(),
        quantity: v.number(),
        unitPrice: v.number(),
      })
    ),
  })
    .index("by_created_by", ["createdBy"])
    .index("by_customer", ["customerId"])
    .index("by_status", ["status"])
    .index("by_invoice_number", ["invoiceNumber"]),

  invoiceItems: defineTable({
    invoiceId: v.id("invoices"),
    productId: v.optional(v.id("products")),
    description: v.string(),
    quantity: v.number(),
    unitPrice: v.number(),
    total: v.number(),
  })
    .index("by_invoice", ["invoiceId"]),

  settings: defineTable({
    companyName: v.string(),
    logoUrl: v.optional(v.string()),
    contactEmail: v.string(),
    contactPhone: v.string(),
    address: v.string(),
    city: v.string(),
    state: v.string(),
    zipCode: v.string(),
    termsUrl: v.optional(v.string()),
    privacyPolicyUrl: v.optional(v.string()),
    invoiceFooter: v.optional(v.string()),
    taxId: v.optional(v.string()),
    paymentTerms: v.string(),
    createdBy: v.id("users"),
  })
    .index("by_created_by", ["createdBy"]),

  settingsHistory: defineTable({
    settingsId: v.id("settings"),
    version: v.number(),
    data: v.any(),
    changedBy: v.id("users"),
    changedAt: v.number(),
  })
    .index("by_settings", ["settingsId"])
    .index("by_version", ["settingsId", "version"]),

  // SMS Templates for common communications
  smsTemplates: defineTable({
    name: v.string(),
    content: v.string(),
    category: v.string(), // appointment, service, invoice, general
    isActive: v.boolean(),
    variables: v.array(v.string()), // Available variables like {customerName}, {appointmentDate}
    description: v.optional(v.string()),
    createdBy: v.id("users"),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_created_by", ["createdBy"])
    .index("by_category", ["category"])
    .index("by_active", ["isActive"]),

  // Email Templates for customizable email communications
  emailTemplates: defineTable({
    name: v.string(),
    subject: v.string(),
    content: v.string(), // HTML content with variable placeholders
    category: v.string(), // invoice, notification, reminder, general
    isActive: v.boolean(),
    variables: v.array(v.string()), // Available variables like {customerName}, {invoiceNumber}, {companyName}
    description: v.optional(v.string()),
    isDefault: v.optional(v.boolean()), // Whether this is a system default template
    createdBy: v.id("users"),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_category", ["category"])
    .index("by_created_by", ["createdBy"])
    .index("by_active", ["isActive"])
    .index("by_default", ["isDefault"]),

  // SMS History for tracking all sent messages
  smsHistory: defineTable({
    customerId: v.id("customers"),
    jobId: v.optional(v.id("jobs")),
    invoiceId: v.optional(v.id("invoices")),
    templateId: v.optional(v.id("smsTemplates")),
    phoneNumber: v.string(),
    message: v.string(),
    status: v.string(), // sent, delivered, failed, pending
    twilioSid: v.optional(v.string()), // Twilio message SID for tracking
    errorMessage: v.optional(v.string()),
    sentBy: v.id("users"),
    sentAt: v.number(),
    deliveredAt: v.optional(v.number()),
  })
    .index("by_customer", ["customerId"])
    .index("by_job", ["jobId"])
    .index("by_invoice", ["invoiceId"])
    .index("by_sent_by", ["sentBy"])
    .index("by_status", ["status"])
    .index("by_sent_at", ["sentAt"]),

  // Twilio Configuration (admin-only, encrypted storage)
  twilioSettings: defineTable({
    accountSid: v.string(),
    authToken: v.string(), // This will be encrypted
    phoneNumber: v.string(),
    isActive: v.boolean(),
    testMode: v.optional(v.boolean()), // For testing with Twilio test credentials
    createdBy: v.id("users"),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_created_by", ["createdBy"])
    .index("by_active", ["isActive"]),

  // Rate Limiting for security
  rateLimits: defineTable({
    userId: v.string(),
    action: v.string(),
    windowStart: v.number(),
    requestCount: v.number(),
    lastRequest: v.number(),
    blocked: v.boolean(),
    blockExpires: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_user_action", ["userId", "action"])
    .index("by_action", ["action"])
    .index("by_last_request", ["lastRequest"]),

  // Email Tracking for monitoring email opens and engagement
  emailTracking: defineTable({
    emailId: v.string(), // Resend email ID
    invoiceId: v.optional(v.id("invoices")), // Associated invoice if applicable
    recipientEmail: v.string(),
    emailType: v.string(), // 'invoice', 'notification', 'reminder', etc.
    sentAt: v.number(),
    openedAt: v.optional(v.number()), // When email was first opened
    openCount: v.number(), // Number of times opened
    lastOpenedAt: v.optional(v.number()), // Most recent open time
    userAgent: v.optional(v.string()), // Browser/client info from open event
    ipAddress: v.optional(v.string()), // IP address from open event (anonymized)
    trackingPixelUrl: v.string(), // Unique tracking pixel URL
    isTracked: v.boolean(), // Whether tracking is enabled for this email
    metadata: v.optional(v.any()), // Additional tracking metadata
    sentBy: v.id("users"),
  })
    .index("by_email_id", ["emailId"])
    .index("by_invoice", ["invoiceId"])
    .index("by_recipient", ["recipientEmail"])
    .index("by_sent_by", ["sentBy"])
    .index("by_sent_at", ["sentAt"])
    .index("by_opened_at", ["openedAt"])
    .index("by_email_type", ["emailType"]),

  // Notifications for real-time updates and alerts
  notifications: defineTable({
    type: v.string(), // 'email_opened', 'invoice_paid', 'system_alert', etc.
    title: v.string(),
    message: v.string(),
    data: v.optional(v.any()), // Additional notification data
    isRead: v.boolean(),
    userId: v.union(v.id("users"), v.null()), // null for broadcast notifications
    createdAt: v.number(),
    readAt: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_type", ["type"])
    .index("by_created_at", ["createdAt"])
    .index("by_is_read", ["isRead"]),

  // Role Management and Approval System
  roleRequests: defineTable({
    userId: v.id("users"),
    requestedRole: v.string(), // The role being requested (typically "admin")
    currentRole: v.string(), // The user's current role
    requestedBy: v.id("users"), // Who made the request (could be self or another user)
    status: v.string(), // "pending", "approved", "rejected"
    reason: v.optional(v.string()), // Reason for the request
    reviewedBy: v.optional(v.id("users")), // Master user who reviewed the request
    reviewedAt: v.optional(v.number()), // When the request was reviewed
    reviewNotes: v.optional(v.string()), // Notes from the reviewer
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_status", ["status"])
    .index("by_requested_by", ["requestedBy"])
    .index("by_reviewed_by", ["reviewedBy"])
    .index("by_created_at", ["createdAt"]),

  // Role Change History for audit trail
  roleHistory: defineTable({
    userId: v.id("users"),
    previousRole: v.string(),
    newRole: v.string(),
    changedBy: v.id("users"), // Master user who made the change
    reason: v.optional(v.string()),
    requestId: v.optional(v.id("roleRequests")), // Link to role request if applicable
    createdAt: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_changed_by", ["changedBy"])
    .index("by_created_at", ["createdAt"]),

  // User Invitations for controlled signup
  userInvitations: defineTable({
    email: v.string(),
    role: v.string(), // "staff" or "admin"
    department: v.optional(v.string()),
    invitedBy: v.id("users"), // Admin/Master who sent the invitation
    status: v.string(), // "pending", "accepted", "expired", "revoked"
    token: v.string(), // Unique invitation token
    expiresAt: v.number(), // Expiration timestamp
    acceptedAt: v.optional(v.number()), // When invitation was accepted
    acceptedBy: v.optional(v.id("users")), // User who accepted the invitation
    message: v.optional(v.string()), // Optional personal message
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_email", ["email"])
    .index("by_token", ["token"])
    .index("by_invited_by", ["invitedBy"])
    .index("by_status", ["status"])
    .index("by_expires_at", ["expiresAt"])
    .index("by_created_at", ["createdAt"]),
};

// Extend users table to include optional fields for backward compatibility
const extendedUsers = defineTable({
  name: v.optional(v.string()),
  image: v.optional(v.string()),
  email: v.optional(v.string()),
  emailVerificationTime: v.optional(v.number()),
  phone: v.optional(v.string()),
  phoneVerificationTime: v.optional(v.number()),
  isAnonymous: v.optional(v.boolean()),
  // Additional optional fields for backward compatibility
  role: v.optional(v.string()),
  bio: v.optional(v.string()),
  department: v.optional(v.string()),
})
  .index("email", ["email"])
  .index("phone", ["phone"]);

export default defineSchema({
  ...authTables,
  users: extendedUsers, // Override the users table with extended fields
  ...applicationTables,
});